

[7/16/2025, 5:26:39 PM] [STARTING] Attempting to start the bot..
NodeJS Version: v20.19.3
Bot Version: 1.8.5[2025-07-16T17:26:47.753Z] [DEBUG] Setting up music play modal handler...
[2025-07-16T17:26:47.754Z] [DEBUG] Registered music modal handler
[2025-07-16T17:26:48.984Z] [DEBUG] Queue persistence enabled - existing queue data will be preserved
[2025-07-16T17:26:48.984Z] [DEBUG] Using existing discord-player instance
[2025-07-16T17:26:48.984Z] [DEBUG] Applying voice connection patches
[2025-07-16T17:26:48.985Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-07-16T17:26:48.985Z] [DEBUG] Voice state update handler patched
[2025-07-16T17:26:48.985Z] [DEBUG] Voice connection patching applied successfully
[2025-07-16T17:26:50.930Z] [DEBUG] Registering player events (first time)
[2025-07-16T17:26:50.930Z] [DEBUG] Registered player event handlers
[2025-07-16T17:26:51.133Z] [DEBUG] Initialized MusicManager
[2025-07-16T17:26:51.133Z] [DEBUG] No saved queues found in MongoDB

[7/16/2025, 5:26:51 PM] [READY] Bot is now online and ready!17:26 (16-07-2025) - Bot started up - Node.js v20.19.3 - App Version 1.8.5
[2025-07-16T17:31:36.432Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-16T17:31:36.432Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-16T17:31:36.456Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-16T17:31:36.456Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-16T17:31:36.456Z] [DEBUG] MongoDB disconnected - queue saves will be disabled


[7/16/2025, 5:31:43 PM] [STARTING] Attempting to start the bot..
NodeJS Version: v20.19.3
Bot Version: 1.8.5[2025-07-16T17:31:50.769Z] [DEBUG] Setting up music play modal handler...
[2025-07-16T17:31:50.769Z] [DEBUG] Registered music modal handler
[2025-07-16T17:31:52.018Z] [DEBUG] Queue persistence enabled - existing queue data will be preserved
[2025-07-16T17:31:52.019Z] [DEBUG] Applying voice connection patches
[2025-07-16T17:31:52.019Z] [DEBUG] Using existing discord-player instance
[2025-07-16T17:31:52.019Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-07-16T17:31:52.019Z] [DEBUG] Voice state update handler patched
[2025-07-16T17:31:52.019Z] [DEBUG] Voice connection patching applied successfully
[2025-07-16T17:31:54.001Z] [DEBUG] Registering player events (first time)
[2025-07-16T17:31:54.001Z] [DEBUG] Registered player event handlers
[2025-07-16T17:31:54.236Z] [DEBUG] No saved queues found in MongoDB
[2025-07-16T17:31:54.236Z] [DEBUG] Initialized MusicManager

[7/16/2025, 5:31:54 PM] [READY] Bot is now online and ready!17:31 (16-07-2025) - Bot started up - Node.js v20.19.3 - App Version 1.8.5
[2025-07-16T17:40:09.754Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-16T17:40:09.755Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-16T17:40:09.777Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-16T17:40:09.777Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-16T17:40:09.777Z] [DEBUG] MongoDB disconnected - queue saves will be disabled


[7/16/2025, 5:40:16 PM] [STARTING] Attempting to start the bot..
NodeJS Version: v20.19.3
Bot Version: 1.8.5[2025-07-16T17:40:24.250Z] [DEBUG] Setting up music play modal handler...
[2025-07-16T17:40:24.251Z] [DEBUG] Registered music modal handler
[2025-07-16T17:40:25.455Z] [DEBUG] Queue persistence enabled - existing queue data will be preserved
[2025-07-16T17:40:25.455Z] [DEBUG] Applying voice connection patches
[2025-07-16T17:40:25.455Z] [DEBUG] Using existing discord-player instance
[2025-07-16T17:40:25.455Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-07-16T17:40:25.455Z] [DEBUG] Voice state update handler patched
[2025-07-16T17:40:25.456Z] [DEBUG] Voice connection patching applied successfully
[2025-07-16T17:40:27.408Z] [DEBUG] Registering player events (first time)
[2025-07-16T17:40:27.408Z] [DEBUG] Registered player event handlers
[2025-07-16T17:40:27.611Z] [DEBUG] No saved queues found in MongoDB
[2025-07-16T17:40:27.611Z] [DEBUG] Initialized MusicManager

[7/16/2025, 5:40:27 PM] [READY] Bot is now online and ready!17:40 (16-07-2025) - Bot started up - Node.js v20.19.3 - App Version 1.8.5
[2025-07-16T17:45:23.256Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-16T17:45:23.256Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-16T17:45:23.281Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-16T17:45:23.281Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-16T17:45:23.282Z] [DEBUG] MongoDB disconnected - queue saves will be disabled


[7/16/2025, 5:45:29 PM] [STARTING] Attempting to start the bot..
NodeJS Version: v20.19.3
Bot Version: 1.8.5[2025-07-16T17:45:37.870Z] [DEBUG] Setting up music play modal handler...
[2025-07-16T17:45:37.870Z] [DEBUG] Registered music modal handler
[2025-07-16T17:45:39.070Z] [DEBUG] Queue persistence enabled - existing queue data will be preserved
[2025-07-16T17:45:39.071Z] [DEBUG] Applying voice connection patches
[2025-07-16T17:45:39.071Z] [DEBUG] Using existing discord-player instance
[2025-07-16T17:45:39.071Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-07-16T17:45:39.071Z] [DEBUG] Voice state update handler patched
[2025-07-16T17:45:39.072Z] [DEBUG] Voice connection patching applied successfully
[2025-07-16T17:45:41.152Z] [DEBUG] Registering player events (first time)
[2025-07-16T17:45:41.152Z] [DEBUG] Registered player event handlers
[2025-07-16T17:45:41.356Z] [DEBUG] No saved queues found in MongoDB
[2025-07-16T17:45:41.356Z] [DEBUG] Initialized MusicManager

[7/16/2025, 5:45:41 PM] [READY] Bot is now online and ready!17:45 (16-07-2025) - Bot started up - Node.js v20.19.3 - App Version 1.8.5
[2025-07-16T17:46:46.076Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-16T17:46:46.076Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-16T17:46:46.104Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-16T17:46:46.104Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-16T17:46:46.104Z] [DEBUG] MongoDB disconnected - queue saves will be disabled


[7/17/2025, 7:25:42 AM] [STARTING] Attempting to start the bot..
NodeJS Version: v20.19.3
Bot Version: 1.8.5[2025-07-17T07:25:50.181Z] [DEBUG] Setting up music play modal handler...
[2025-07-17T07:25:50.181Z] [DEBUG] Registered music modal handler
[2025-07-17T07:25:51.481Z] [DEBUG] Queue persistence enabled - existing queue data will be preserved
[2025-07-17T07:25:51.482Z] [DEBUG] Using existing discord-player instance
[2025-07-17T07:25:51.482Z] [DEBUG] Applying voice connection patches
[2025-07-17T07:25:51.482Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-07-17T07:25:51.482Z] [DEBUG] Voice state update handler patched
[2025-07-17T07:25:51.483Z] [DEBUG] Voice connection patching applied successfully
[2025-07-17T07:25:53.376Z] [DEBUG] Registering player events (first time)
[2025-07-17T07:25:53.376Z] [DEBUG] Registered player event handlers
[2025-07-17T07:25:53.578Z] [DEBUG] No saved queues found in MongoDB
[2025-07-17T07:25:53.578Z] [DEBUG] Initialized MusicManager

[7/17/2025, 7:25:53 AM] [READY] Bot is now online and ready!07:25 (17-07-2025) - Bot started up - Node.js v20.19.3 - App Version 1.8.5
[2025-07-17T07:34:08.966Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-17T07:34:08.966Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-17T07:34:08.995Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-17T07:34:08.995Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-17T07:34:08.995Z] [DEBUG] MongoDB disconnected - queue saves will be disabled


[7/17/2025, 7:34:15 AM] [STARTING] Attempting to start the bot..
NodeJS Version: v20.19.3
Bot Version: 1.8.5[2025-07-17T07:34:23.789Z] [DEBUG] Setting up music play modal handler...
[2025-07-17T07:34:23.790Z] [DEBUG] Registered music modal handler
[2025-07-17T07:34:25.119Z] [DEBUG] Queue persistence enabled - existing queue data will be preserved
[2025-07-17T07:34:25.119Z] [DEBUG] Using existing discord-player instance
[2025-07-17T07:34:25.119Z] [DEBUG] Applying voice connection patches
[2025-07-17T07:34:25.120Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-07-17T07:34:25.120Z] [DEBUG] Voice state update handler patched
[2025-07-17T07:34:25.120Z] [DEBUG] Voice connection patching applied successfully
[2025-07-17T07:34:27.025Z] [DEBUG] Registering player events (first time)
[2025-07-17T07:34:27.025Z] [DEBUG] Registered player event handlers
[2025-07-17T07:34:27.228Z] [DEBUG] Initialized MusicManager
[2025-07-17T07:34:27.228Z] [DEBUG] No saved queues found in MongoDB

[7/17/2025, 7:34:27 AM] [READY] Bot is now online and ready!07:34 (17-07-2025) - Bot started up - Node.js v20.19.3 - App Version 1.8.5
[2025-07-17T07:40:32.087Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-17T07:40:32.087Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-17T07:40:32.123Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-17T07:40:32.123Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-17T07:40:32.123Z] [DEBUG] MongoDB disconnected - queue saves will be disabled


[7/17/2025, 7:40:39 AM] [STARTING] Attempting to start the bot..
NodeJS Version: v20.19.3
Bot Version: 1.8.5[2025-07-17T07:40:48.564Z] [DEBUG] Setting up music play modal handler...
[2025-07-17T07:40:48.564Z] [DEBUG] Registered music modal handler
[2025-07-17T07:40:49.934Z] [DEBUG] Queue persistence enabled - existing queue data will be preserved
[2025-07-17T07:40:49.934Z] [DEBUG] Using existing discord-player instance
[2025-07-17T07:40:49.934Z] [DEBUG] Applying voice connection patches
[2025-07-17T07:40:49.935Z] [DEBUG] Voice state update handler patched
[2025-07-17T07:40:49.935Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-07-17T07:40:49.935Z] [DEBUG] Voice connection patching applied successfully
[2025-07-17T07:40:51.807Z] [DEBUG] Registering player events (first time)
[2025-07-17T07:40:51.807Z] [DEBUG] Registered player event handlers
[2025-07-17T07:40:52.011Z] [DEBUG] No saved queues found in MongoDB
[2025-07-17T07:40:52.011Z] [DEBUG] Initialized MusicManager

[7/17/2025, 7:40:52 AM] [READY] Bot is now online and ready!07:40 (17-07-2025) - Bot started up - Node.js v20.19.3 - App Version 1.8.5
[2025-07-17T07:44:02.174Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-17T07:44:02.174Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-17T07:44:02.200Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-17T07:44:02.200Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-17T07:44:02.200Z] [DEBUG] MongoDB disconnected - queue saves will be disabled


[7/17/2025, 7:44:09 AM] [STARTING] Attempting to start the bot..
NodeJS Version: v20.19.3
Bot Version: 1.8.5[2025-07-17T07:44:16.647Z] [DEBUG] Setting up music play modal handler...
[2025-07-17T07:44:16.647Z] [DEBUG] Registered music modal handler
[2025-07-17T07:44:18.056Z] [DEBUG] Queue persistence enabled - existing queue data will be preserved
[2025-07-17T07:44:18.056Z] [DEBUG] Using existing discord-player instance
[2025-07-17T07:44:18.056Z] [DEBUG] Applying voice connection patches
[2025-07-17T07:44:18.056Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-07-17T07:44:18.057Z] [DEBUG] Voice state update handler patched
[2025-07-17T07:44:18.057Z] [DEBUG] Voice connection patching applied successfully
[2025-07-17T07:44:19.927Z] [DEBUG] Registering player events (first time)
[2025-07-17T07:44:19.927Z] [DEBUG] Registered player event handlers
[2025-07-17T07:44:20.129Z] [DEBUG] No saved queues found in MongoDB
[2025-07-17T07:44:20.129Z] [DEBUG] Initialized MusicManager

[7/17/2025, 7:44:20 AM] [READY] Bot is now online and ready!07:44 (17-07-2025) - Bot started up - Node.js v20.19.3 - App Version 1.8.5
[2025-07-17T07:45:49.111Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-17T07:45:49.112Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-17T07:45:49.136Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-17T07:45:49.136Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-17T07:45:49.136Z] [DEBUG] MongoDB disconnected - queue saves will be disabled


[7/17/2025, 7:45:55 AM] [STARTING] Attempting to start the bot..
NodeJS Version: v20.19.3
Bot Version: 1.8.5[2025-07-17T07:46:03.264Z] [DEBUG] Setting up music play modal handler...
[2025-07-17T07:46:03.265Z] [DEBUG] Registered music modal handler
[2025-07-17T07:46:04.733Z] [DEBUG] Queue persistence enabled - existing queue data will be preserved
[2025-07-17T07:46:04.733Z] [DEBUG] Using existing discord-player instance
[2025-07-17T07:46:04.734Z] [DEBUG] Applying voice connection patches
[2025-07-17T07:46:04.734Z] [DEBUG] Voice state update handler patched
[2025-07-17T07:46:04.734Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-07-17T07:46:04.734Z] [DEBUG] Voice connection patching applied successfully
[2025-07-17T07:46:06.466Z] [DEBUG] Registering player events (first time)
[2025-07-17T07:46:06.467Z] [DEBUG] Registered player event handlers
[2025-07-17T07:46:06.670Z] [DEBUG] No saved queues found in MongoDB
[2025-07-17T07:46:06.670Z] [DEBUG] Initialized MusicManager

[7/17/2025, 7:46:07 AM] [READY] Bot is now online and ready!07:46 (17-07-2025) - Bot started up - Node.js v20.19.3 - App Version 1.8.5
[2025-07-17T07:51:18.042Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-17T07:51:18.042Z] [DEBUG] SIGINT signal received - graceful shutdown in progress
[2025-07-17T07:51:18.067Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-17T07:51:18.067Z] [DEBUG] MongoDB disconnected - queue saves will be disabled
[2025-07-17T07:51:18.067Z] [DEBUG] MongoDB disconnected - queue saves will be disabled


[7/17/2025, 7:51:24 AM] [STARTING] Attempting to start the bot..
NodeJS Version: v20.19.3
Bot Version: 1.8.5[2025-07-17T07:51:32.565Z] [DEBUG] Setting up music play modal handler...
[2025-07-17T07:51:32.565Z] [DEBUG] Registered music modal handler
[2025-07-17T07:51:33.981Z] [DEBUG] Queue persistence enabled - existing queue data will be preserved
[2025-07-17T07:51:33.981Z] [DEBUG] Using existing discord-player instance
[2025-07-17T07:51:33.982Z] [DEBUG] Applying voice connection patches
[2025-07-17T07:51:33.982Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-07-17T07:51:33.982Z] [DEBUG] Voice state update handler patched
[2025-07-17T07:51:33.982Z] [DEBUG] Voice connection patching applied successfully
[2025-07-17T07:51:35.752Z] [DEBUG] Registering player events (first time)
[2025-07-17T07:51:35.752Z] [DEBUG] Registered player event handlers
[2025-07-17T07:51:35.957Z] [DEBUG] No saved queues found in MongoDB
[2025-07-17T07:51:35.957Z] [DEBUG] Initialized MusicManager

[7/17/2025, 7:51:36 AM] [READY] Bot is now online and ready!07:51 (17-07-2025) - Bot started up - Node.js v20.19.3 - App Version 1.8.5
2025-07-17T07:51:56.824Z - ERROR: TypeError: Cannot read properties of undefined (reading 'send')


[22:23:14 19/8/2025] [STARTING] Attempting to start the bot..
NodeJS Version: v20.15.0
Bot Version: 1.8.5[2025-08-19T15:24:11.174Z] [DEBUG] Đang thiết lập trình xử lý modal phát nhạc...
[2025-08-19T15:24:11.174Z] [DEBUG] Đã đăng ký trình xử lý modal nhạc


[11:57:15 21/8/2025] [STARTING] Attempting to start the bot..
NodeJS Version: v20.15.0
Bot Version: 1.8.5[2025-08-21T04:57:52.752Z] [DEBUG] Đang thiết lập trình xử lý modal phát nhạc...
[2025-08-21T04:57:52.759Z] [DEBUG] Đã đăng ký trình xử lý modal nhạc
[2025-08-21T04:57:54.687Z] [DEBUG] Đã bật tính năng duy trì hàng đợi - dữ liệu hàng đợi hiện có sẽ được giữ lại
[2025-08-21T04:57:54.688Z] [DEBUG] Sử dụng phiên bản discord-player hiện có
[2025-08-21T04:57:54.688Z] [DEBUG] Applying voice connection patches
[2025-08-21T04:57:54.689Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-08-21T04:57:54.689Z] [DEBUG] Voice state update handler patched
[2025-08-21T04:57:54.690Z] [DEBUG] Voice connection patching applied successfully
[2025-08-21T04:57:57.240Z] [DEBUG] Registering player events (first time)
[2025-08-21T04:57:57.240Z] [DEBUG] Registered player event handlers
[2025-08-21T04:57:57.535Z] [DEBUG] Đã khởi tạo MusicManager
[2025-08-21T04:57:57.535Z] [DEBUG] No saved queues found in MongoDB

[11:57:58 21/8/2025] [SẴN SÀNG] Bot đã trực tuyến và sẵn sàng!2025-08-21T04:57:58.233Z - ERROR: DiscordAPIError[10004]: Unknown Guild
2025-08-21T04:57:58.528Z - ERROR: DiscordAPIError[10004]: Unknown Guild
2025-08-21T04:57:59.993Z - ERROR: DiscordAPIError[10004]: Unknown Guild
2025-08-21T04:58:00.338Z - ERROR: DiscordAPIError[10004]: Unknown Guild
[2025-08-21T04:58:26.856Z] [DEBUG] Đã nhận được tín hiệu SIGINT - đang tiến hành tắt máy một cách duyên dáng
[2025-08-21T04:58:26.856Z] [DEBUG] Đã nhận được tín hiệu SIGINT - đang tiến hành tắt máy một cách duyên dáng
[2025-08-21T04:58:26.920Z] [DEBUG] MongoDB đã ngắt kết nối - lưu hàng đợi sẽ bị vô hiệu hóa
[2025-08-21T04:58:26.921Z] [DEBUG] MongoDB đã ngắt kết nối - lưu hàng đợi sẽ bị vô hiệu hóa
[2025-08-21T04:58:26.922Z] [DEBUG] MongoDB đã ngắt kết nối - lưu hàng đợi sẽ bị vô hiệu hóa


[11:58:57 21/8/2025] [STARTING] Attempting to start the bot..
NodeJS Version: v20.15.0
Bot Version: 1.8.5[2025-08-21T04:59:04.952Z] [DEBUG] Đang thiết lập trình xử lý modal phát nhạc...
[2025-08-21T04:59:04.953Z] [DEBUG] Đã đăng ký trình xử lý modal nhạc
[2025-08-21T04:59:06.685Z] [DEBUG] Đã bật tính năng duy trì hàng đợi - dữ liệu hàng đợi hiện có sẽ được giữ lại
[2025-08-21T04:59:06.686Z] [DEBUG] Sử dụng phiên bản discord-player hiện có
[2025-08-21T04:59:06.686Z] [DEBUG] Applying voice connection patches
[2025-08-21T04:59:06.687Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-08-21T04:59:06.687Z] [DEBUG] Voice state update handler patched
[2025-08-21T04:59:06.688Z] [DEBUG] Voice connection patching applied successfully
[2025-08-21T04:59:09.108Z] [DEBUG] Registering player events (first time)
[2025-08-21T04:59:09.108Z] [DEBUG] Registered player event handlers
[2025-08-21T04:59:09.628Z] [DEBUG] No saved queues found in MongoDB
[2025-08-21T04:59:09.629Z] [DEBUG] Đã khởi tạo MusicManager

[11:59:10 21/8/2025] [SẴN SÀNG] Bot đã trực tuyến và sẵn sàng![2025-08-21T05:00:35.965Z] [DEBUG] Đã nhận được tín hiệu SIGINT - đang tiến hành tắt máy một cách duyên dáng
[2025-08-21T05:00:35.965Z] [DEBUG] Đã nhận được tín hiệu SIGINT - đang tiến hành tắt máy một cách duyên dáng
[2025-08-21T05:00:35.974Z] [DEBUG] MongoDB đã ngắt kết nối - lưu hàng đợi sẽ bị vô hiệu hóa
[2025-08-21T05:00:35.974Z] [DEBUG] MongoDB đã ngắt kết nối - lưu hàng đợi sẽ bị vô hiệu hóa
[2025-08-21T05:00:35.974Z] [DEBUG] MongoDB đã ngắt kết nối - lưu hàng đợi sẽ bị vô hiệu hóa


[12:00:45 21/8/2025] [STARTING] Attempting to start the bot..
NodeJS Version: v20.15.0
Bot Version: 1.8.5[2025-08-21T05:00:52.497Z] [DEBUG] Đang thiết lập trình xử lý modal phát nhạc...
[2025-08-21T05:00:52.497Z] [DEBUG] Đã đăng ký trình xử lý modal nhạc
[2025-08-21T05:00:54.214Z] [DEBUG] Đã bật tính năng duy trì hàng đợi - dữ liệu hàng đợi hiện có sẽ được giữ lại
[2025-08-21T05:00:54.216Z] [DEBUG] Patched joinVoiceChannel function for connection tracking
[2025-08-21T05:00:54.214Z] [DEBUG] Sử dụng phiên bản discord-player hiện có
[2025-08-21T05:00:54.216Z] [DEBUG] Voice state update handler patched
[2025-08-21T05:00:54.215Z] [DEBUG] Applying voice connection patches
[2025-08-21T05:00:54.217Z] [DEBUG] Voice connection patching applied successfully
[2025-08-21T05:00:56.628Z] [DEBUG] Registering player events (first time)
[2025-08-21T05:00:56.628Z] [DEBUG] Registered player event handlers
[2025-08-21T05:00:57.133Z] [DEBUG] No saved queues found in MongoDB
[2025-08-21T05:00:57.133Z] [DEBUG] Đã khởi tạo MusicManager

[12:00:57 21/8/2025] [SẴN SÀNG] Bot đã trực tuyến và sẵn sàng![2025-08-21T05:04:00.873Z] [DEBUG] Đã nhận được tín hiệu SIGINT - đang tiến hành tắt máy một cách duyên dáng
[2025-08-21T05:04:00.874Z] [DEBUG] Đã nhận được tín hiệu SIGINT - đang tiến hành tắt máy một cách duyên dáng
[2025-08-21T05:04:00.888Z] [DEBUG] MongoDB đã ngắt kết nối - lưu hàng đợi sẽ bị vô hiệu hóa
[2025-08-21T05:04:00.889Z] [DEBUG] MongoDB đã ngắt kết nối - lưu hàng đợi sẽ bị vô hiệu hóa
[2025-08-21T05:04:00.889Z] [DEBUG] MongoDB đã ngắt kết nối - lưu hàng đợi sẽ bị vô hiệu hóa
